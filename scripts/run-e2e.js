#!/usr/bin/env node

const { spawn } = require('child_process');
const os = require('os');
const path = require('path');

/**
 * Cross-platform script to run e2e tests without stealing window focus
 *
 * On macOS/Linux: Uses xvfb-run if available to run in virtual display
 * On Windows: Runs normally (Windows handles this better)
 * Fallback: Runs without xvfb if not available
 */

function runCommand(command, args, options = {}) {
  return new Promise((resolve, reject) => {
    console.log(`🚀 Running: ${command} ${args.join(' ')}`);

    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: false,  // Disable shell to avoid security issues
      ...options
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve(code);
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
}

async function checkXvfbAvailable() {
  try {
    await runCommand('which', ['xvfb-run'], { stdio: 'pipe' });
    return true;
  } catch {
    try {
      // Try using /bin/sh to run command -v
      await runCommand('/bin/sh', ['-c', 'command -v xvfb-run'], { stdio: 'pipe' });
      return true;
    } catch {
      return false;
    }
  }
}

async function main() {
  const platform = os.platform();
  const args = process.argv.slice(2);

  // Base command
  const baseCommand = ['npx', 'vitest', 'run', '--config', 'vitest.playwright.config.mjs'];
  const fullCommand = [...baseCommand, ...args];

  console.log(`🖥️  Platform: ${platform}`);

  // On Windows, run normally (Windows handles focus better)
  if (platform === 'win32') {
    console.log('🪟 Running on Windows - no virtual display needed');
    await runCommand(fullCommand[0], fullCommand.slice(1));
    return;
  }

  // On macOS, use environment variables to minimize focus stealing
  if (platform === 'darwin') {
    console.log('🍎 Running on macOS - using background execution mode');
    console.log('💡 Note: Electron apps may still briefly appear but should not steal focus');

    // Set environment variables to minimize focus stealing on macOS
    const macOSEnv = {
      ...process.env,
      // Prevent Electron from bringing windows to front
      ELECTRON_DISABLE_SECURITY_WARNINGS: 'true',
      // Run in background mode
      NSAppSleepDisabled: 'YES',
      // Minimize window activation
      ELECTRON_NO_ATTACH_CONSOLE: '1'
    };

    await runCommand(fullCommand[0], fullCommand.slice(1), { env: macOSEnv });
    return;
  }

  // On Linux, try to use xvfb-run for virtual display
  const hasXvfb = await checkXvfbAvailable();

  if (hasXvfb) {
    console.log('🐧 Using xvfb-run to prevent focus stealing on Linux');
    // Use xvfb-run with auto-servernum and screen size
    const xvfbCommand = [
      'xvfb-run',
      '-a',                    // auto-servernum
      '--server-args=-screen 0 1920x1080x24',  // screen size
      ...fullCommand
    ];
    await runCommand(xvfbCommand[0], xvfbCommand.slice(1));
  } else {
    console.log('⚠️  xvfb-run not available, running without virtual display');
    console.log('💡 To prevent focus stealing on Linux, install xvfb:');
    console.log('   sudo apt-get install xvfb  # Ubuntu/Debian');
    console.log('   sudo yum install xorg-x11-server-Xvfb  # CentOS/RHEL');
    await runCommand(fullCommand[0], fullCommand.slice(1));
  }
}

main().catch((error) => {
  console.error('❌ Error running e2e tests:', error.message);
  process.exit(1);
});
